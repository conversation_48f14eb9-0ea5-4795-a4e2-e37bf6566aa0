#!/bin/bash

# MirrorGo 开发环境启动脚本

echo "🚀 MirrorGo Development Environment"
echo "=================================="

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "❌ config.yaml not found!"
    echo "Please create config.yaml first."
    exit 1
fi

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed!"
    echo "Please install Go first: https://golang.org/dl/"
    exit 1
fi

# 安装依赖
echo "📦 Installing dependencies..."
go mod tidy

# 检查依赖
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies!"
    exit 1
fi

# 启动服务
echo "🚀 Starting MirrorGo..."
echo ""
echo "📋 Control Panel will be available at: http://localhost:8080"
echo "🔧 Press Ctrl+C to stop"
echo ""

# 运行程序
go run main_simple.go
