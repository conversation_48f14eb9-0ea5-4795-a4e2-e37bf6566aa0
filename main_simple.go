package main

import (
	"compress/flate"
	"compress/gzip"
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"regexp"
	"strings"
	"sync"
	"syscall"
	"time"
)

// 简化配置结构 - 硬编码配置，避免YAML依赖
type Config struct {
	ControlPort   int
	UpstreamProxy string
	DebugMode     bool
	DomainPorts   map[string]int
}

// 获取默认配置
func getDefaultConfig() *Config {
	return &Config{
		ControlPort:   8080,
		UpstreamProxy: "http://127.0.0.1:7890", // 你的代理地址
		DebugMode:     true,
		DomainPorts: map[string]int{
			// GitHub 相关域名
			"github.com":                    8081,
			"api.github.com":                8082,
			"avatars.githubusercontent.com": 8083,
			"github.githubassets.com":       8084,
			"raw.githubusercontent.com":     8085,
			"codeload.github.com":           8086,
			"objects.githubusercontent.com": 8087,

			// Google 相关域名
			"google.com":           8088,
			"www.google.com":       8089,
			"fonts.googleapis.com": 8090,
			"ajax.googleapis.com":  8091,

			// Stack Overflow 相关域名
			"stackoverflow.com": 8092,
			"cdn.sstatic.net":   8093,
			"stackexchange.com": 8094,
		},
	}
}

// ProxyManager 代理管理器
type ProxyManager struct {
	config  *Config
	servers map[int]*http.Server
	mutex   sync.RWMutex
}

// NewProxyManager 创建代理管理器
func NewProxyManager(config *Config) *ProxyManager {
	return &ProxyManager{
		config:  config,
		servers: make(map[int]*http.Server),
	}
}

// GetPortForDomain 获取域名对应的端口
func (pm *ProxyManager) GetPortForDomain(domain string) (int, bool) {
	port, exists := pm.config.DomainPorts[domain]
	return port, exists
}

// StartProxyServer 启动代理服务器
func (pm *ProxyManager) StartProxyServer(port int, domain string) {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		pm.handleProxyRequest(w, r, domain)
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	pm.mutex.Lock()
	pm.servers[port] = server
	pm.mutex.Unlock()

	log.Printf("🚀 Starting proxy server for %s on port %d", domain, port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Printf("❌ Server error for %s on port %d: %v", domain, port, err)
	}
}

// handleProxyRequest 处理代理请求
func (pm *ProxyManager) handleProxyRequest(w http.ResponseWriter, r *http.Request, domain string) {
	// 构造目标URL
	targetURL := fmt.Sprintf("https://%s%s", domain, r.URL.Path)
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	if pm.config.DebugMode {
		log.Printf("[%s] %s %s -> %s", domain, r.Method, r.URL.Path, targetURL)
	}

	// 创建代理请求
	proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}

	// 复制请求头
	pm.copyRequestHeaders(proxyReq, r, domain)

	// 发送请求
	client := pm.createHTTPClient(domain)
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("[%s] Request failed: %v", domain, err)
		http.Error(w, "Failed to reach target: "+err.Error(), http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := pm.readResponseBody(resp)
	if err != nil {
		log.Printf("[%s] Failed to read response: %v", domain, err)
		http.Error(w, "Failed to read response", http.StatusInternalServerError)
		return
	}

	// 处理内容重写
	processedBody := pm.processResponseBody(body, resp.Header.Get("Content-Type"), domain)

	// 复制响应头
	pm.copyResponseHeaders(w, resp)
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(processedBody)))

	// 写入响应
	w.WriteHeader(resp.StatusCode)
	w.Write(processedBody)
}

// copyRequestHeaders 复制请求头
func (pm *ProxyManager) copyRequestHeaders(proxyReq *http.Request, originalReq *http.Request, domain string) {
	for name, values := range originalReq.Header {
		lowerName := strings.ToLower(name)
		if lowerName == "host" {
			proxyReq.Header.Set("Host", domain)
			continue
		}
		if strings.HasPrefix(lowerName, "x-forwarded") || lowerName == "x-real-ip" {
			continue
		}
		for _, value := range values {
			proxyReq.Header.Add(name, value)
		}
	}

	// 设置标准头部
	proxyReq.Header.Set("User-Agent", "MirrorGo/1.0-dev")
	proxyReq.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	proxyReq.Header.Set("Accept-Language", "en-US,en;q=0.5")
	proxyReq.Header.Set("Accept-Encoding", "identity")
	proxyReq.Header.Set("DNT", "1")
	proxyReq.Header.Set("Connection", "keep-alive")
}

// copyResponseHeaders 复制响应头
func (pm *ProxyManager) copyResponseHeaders(w http.ResponseWriter, resp *http.Response) {
	for k, vv := range resp.Header {
		lowerK := strings.ToLower(k)

		// 移除安全头
		if lowerK == "content-security-policy" ||
			lowerK == "x-frame-options" ||
			strings.HasPrefix(lowerK, "access-control-") ||
			lowerK == "content-encoding" ||
			lowerK == "content-length" {
			continue
		}

		for _, v := range vv {
			w.Header().Add(k, v)
		}
	}
	w.Header().Set("Content-Encoding", "identity")
}

// createHTTPClient 创建HTTP客户端
func (pm *ProxyManager) createHTTPClient(domain string) *http.Client {
	transport := &http.Transport{
		Dial: (&net.Dialer{
			Timeout:   15 * time.Second,
			KeepAlive: 30 * time.Second,
		}).Dial,
		TLSHandshakeTimeout:   20 * time.Second,
		ResponseHeaderTimeout: 15 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		DisableCompression:    true,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			ServerName:         domain,
		},
	}

	// 设置上游代理
	if pm.config.UpstreamProxy != "" {
		if proxyURL, err := url.Parse(pm.config.UpstreamProxy); err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
			if pm.config.DebugMode {
				log.Printf("🔗 Using upstream proxy: %s", pm.config.UpstreamProxy)
			}
		}
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("stopped after 10 redirects")
			}
			return nil
		},
	}
}

// readResponseBody 读取并解压缩响应体
func (pm *ProxyManager) readResponseBody(resp *http.Response) ([]byte, error) {
	var reader io.Reader = resp.Body

	switch strings.ToLower(resp.Header.Get("Content-Encoding")) {
	case "gzip":
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, err
		}
		defer gzipReader.Close()
		reader = gzipReader
	case "deflate":
		reader = flate.NewReader(resp.Body)
	}

	return io.ReadAll(reader)
}

// processResponseBody 处理响应体内容重写
func (pm *ProxyManager) processResponseBody(body []byte, contentType, currentDomain string) []byte {
	if !strings.Contains(contentType, "text/html") {
		return body
	}

	content := string(body)
	content = pm.rewriteHTMLContent(content, currentDomain)
	return []byte(content)
}

// rewriteHTMLContent 重写HTML内容中的URL
func (pm *ProxyManager) rewriteHTMLContent(content, currentDomain string) string {
	// URL重写模式
	patterns := []struct {
		regex *regexp.Regexp
		desc  string
	}{
		{regexp.MustCompile(`href="https?://([^"]+)"`), "href"},
		{regexp.MustCompile(`src="https?://([^"]+)"`), "src"},
		{regexp.MustCompile(`action="https?://([^"]+)"`), "action"},
		{regexp.MustCompile(`url\(["']?https?://([^"')]+)["']?\)`), "css_url"},
		{regexp.MustCompile(`content="https?://([^"]+)"`), "content"},
	}

	for _, pattern := range patterns {
		content = pattern.regex.ReplaceAllStringFunc(content, func(match string) string {
			return pm.rewriteURLMatch(match, pattern.regex, currentDomain)
		})
	}

	return content
}

// rewriteURLMatch 重写匹配到的URL
func (pm *ProxyManager) rewriteURLMatch(match string, regex *regexp.Regexp, currentDomain string) string {
	submatches := regex.FindStringSubmatch(match)
	if len(submatches) < 2 {
		return match
	}

	urlPart := submatches[1]
	parts := strings.SplitN(urlPart, "/", 2)
	domain := parts[0]

	// 检查是否应该重写这个域名
	port, shouldRewrite := pm.GetPortForDomain(domain)
	if !shouldRewrite {
		return match // 保持外部链接不变
	}

	// 构造新的本地URL
	newURLPart := fmt.Sprintf("http://localhost:%d", port)
	if len(parts) > 1 {
		newURLPart += "/" + parts[1]
	}

	if pm.config.DebugMode {
		log.Printf("🔄 URL rewrite: %s -> %s", urlPart, newURLPart)
	}

	return strings.Replace(match, urlPart, newURLPart, 1)
}

// GetDomainList 获取域名列表
func (pm *ProxyManager) GetDomainList() map[string]int {
	return pm.config.DomainPorts
}

// Shutdown 关闭所有服务器
func (pm *ProxyManager) Shutdown() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for port, server := range pm.servers {
		log.Printf("🛑 Shutting down server on port %d", port)
		server.Close()
	}
}

// handleMainPage 处理控制面板
func handleMainPage(w http.ResponseWriter, r *http.Request, manager *ProxyManager) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	domains := manager.GetDomainList()

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprintf(w, `<!DOCTYPE html>
<html>
<head>
    <title>MirrorGo - Development Environment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { color: #333; border-bottom: 3px solid #007acc; padding-bottom: 15px; margin-bottom: 30px; }
        .section { margin: 25px 0; }
        .domain-list { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }
        .domain-item { margin: 10px 0; padding: 15px; background: white; border-radius: 5px; border: 1px solid #e0e0e0; }
        .port { color: #007acc; font-weight: bold; font-size: 1.1em; }
        .usage { background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8; }
        code { background: #f1f1f1; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace; }
        .status { background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; }
        .btn { display: inline-block; padding: 8px 16px; background: #007acc; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #005a9e; }
        .debug { background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🚀 MirrorGo - Development Environment (Simplified)</h1>

        <div class="section">
            <div class="status">
                <h3>📊 System Status</h3>
                <p>✅ Control server running on port %d</p>
                <p>🔧 Mode: Port-based (Development)</p>
                <p>📡 Configured domains: %d</p>
                <p>🔗 Upstream proxy: %s</p>
            </div>
        </div>

        <div class="section">
            <h2>🌐 Domain Port Mapping</h2>
            <div class="domain-list">`, manager.config.ControlPort, len(domains), manager.config.UpstreamProxy)

	if len(domains) == 0 {
		fmt.Fprintf(w, `                <p>No domains configured!</p>`)
	} else {
		for domain, port := range domains {
			fmt.Fprintf(w, `                <div class="domain-item">
                    <strong>%s</strong> → <span class="port">localhost:%d</span>
                    <br><small>Direct access: <a href="http://localhost:%d/" target="_blank" class="btn">Open http://localhost:%d/</a></small>
                    <br><small>Entry point: <a href="/proxy/%s" target="_blank" class="btn">Start Proxy</a></small>
                </div>`, domain, port, port, port, domain)
		}
	}

	fmt.Fprintf(w, `            </div>
        </div>

        <div class="section">
            <h2>📖 Development Guide</h2>
            <div class="usage">
                <h3>🎯 Quick Start:</h3>
                <p><a href="/proxy/github.com" class="btn">Start GitHub Proxy</a></p>
                <p><a href="/proxy/google.com" class="btn">Start Google Proxy</a></p>

                <h3>🔧 Manual Entry Points:</h3>
                <p><code>http://localhost:%d/proxy/github.com</code></p>
                <p><code>http://localhost:%d/proxy/stackoverflow.com</code></p>

                <h3>🐛 Debug Features:</h3>
                <ul>
                    <li>✅ URL rewrite debugging enabled</li>
                    <li>✅ Access logging enabled</li>
                    <li>✅ Upstream proxy: %s</li>
                    <li>✅ No external dependencies</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <div class="debug">
                <h3>🔧 Configuration (Hardcoded)</h3>
                <p><strong>Control Port:</strong> %d</p>
                <p><strong>Upstream Proxy:</strong> %s</p>
                <p><strong>Debug Mode:</strong> %t</p>
                <p><strong>Note:</strong> This is a simplified version without YAML dependencies</p>
            </div>
        </div>
    </div>
</body>
</html>`, manager.config.ControlPort, manager.config.ControlPort, manager.config.UpstreamProxy, manager.config.ControlPort, manager.config.UpstreamProxy, manager.config.DebugMode)
}

// handleProxyEntry 处理代理入口
func handleProxyEntry(w http.ResponseWriter, r *http.Request, manager *ProxyManager) {
	// 解析域名
	path := strings.TrimPrefix(r.URL.Path, "/proxy/")
	parts := strings.SplitN(path, "/", 2)

	if len(parts) < 1 || parts[0] == "" {
		http.Error(w, "Invalid proxy URL. Use: /proxy/domain.com", http.StatusBadRequest)
		return
	}

	domain := parts[0]
	targetPath := "/"
	if len(parts) >= 2 {
		targetPath = "/" + parts[1]
	}

	// 获取端口
	port, exists := manager.GetPortForDomain(domain)
	if !exists {
		http.Error(w, fmt.Sprintf("Domain %s not configured", domain), http.StatusNotFound)
		return
	}

	// 重定向到对应端口
	redirectURL := fmt.Sprintf("http://localhost:%d%s", port, targetPath)
	if r.URL.RawQuery != "" {
		redirectURL += "?" + r.URL.RawQuery
	}

	log.Printf("🔄 Redirecting %s to port %d: %s", domain, port, redirectURL)
	http.Redirect(w, r, redirectURL, http.StatusFound)
}

func main() {
	// 获取默认配置
	config := getDefaultConfig()

	// 创建代理管理器
	manager := NewProxyManager(config)

	// 启动所有配置的代理服务器
	for domain, port := range config.DomainPorts {
		go manager.StartProxyServer(port, domain)
		time.Sleep(100 * time.Millisecond) // 避免端口冲突
	}

	// 创建控制服务器
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		handleMainPage(w, r, manager)
	})
	http.HandleFunc("/proxy/", func(w http.ResponseWriter, r *http.Request) {
		handleProxyEntry(w, r, manager)
	})

	// 启动控制服务器
	go func() {
		log.Println("🚀 MirrorGo Development Environment Starting...")
		log.Printf("📋 Control Panel: http://localhost:%d", config.ControlPort)
		log.Printf("🔧 Mode: Port-based (Simplified)")
		log.Printf("🔗 Upstream Proxy: %s", config.UpstreamProxy)
		log.Printf("📊 Configured Domains: %d", len(config.DomainPorts))
		log.Println("")
		log.Println("🎯 Quick Start:")
		log.Printf("   http://localhost:%d/proxy/github.com", config.ControlPort)
		log.Printf("   http://localhost:%d/proxy/google.com", config.ControlPort)
		log.Println("")

		addr := fmt.Sprintf(":%d", config.ControlPort)
		if err := http.ListenAndServe(addr, nil); err != nil {
			log.Fatalf("❌ Control server failed: %v", err)
		}
	}()

	// 等待中断信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	log.Println("🛑 Shutting down...")
	manager.Shutdown()
	log.Println("✅ Shutdown complete")
}
