# 🚀 MirrorGo 快速启动指南

## ✅ 问题已解决！

依赖问题已修复，现在使用简化版本（无外部依赖）。

## 🎯 立即开始

### 1. 启动服务
```bash
# 方法1: 直接运行
go run main_simple.go

# 方法2: 构建后运行
go build main_simple.go
./main_simple

# 方法3: 使用脚本
chmod +x start.sh
./start.sh
```

### 2. 访问控制面板
打开浏览器访问: **http://localhost:8080**

### 3. 测试代理功能
点击控制面板中的按钮，或直接访问：
- **GitHub**: http://localhost:8080/proxy/github.com
- **Google**: http://localhost:8080/proxy/google.com

## 🔧 配置说明

### 硬编码配置（在 main_simple.go 中）
```go
// 上游代理配置
UpstreamProxy: "http://127.0.0.1:7890"

// 域名端口映射
DomainPorts: map[string]int{
    "github.com":                    8081,
    "api.github.com":                8082,
    "avatars.githubusercontent.com": 8083,
    "github.githubassets.com":       8084,
    // ... 更多域名
}
```

### 修改配置
如需修改代理地址或端口映射，编辑 `main_simple.go` 中的 `getDefaultConfig()` 函数。

## 🎯 测试步骤

### 1. 验证服务启动
```bash
go run main_simple.go
```
应该看到类似输出：
```
🚀 Starting proxy server for github.com on port 8081
🚀 Starting proxy server for api.github.com on port 8082
📋 Control Panel: http://localhost:8080
🔗 Upstream Proxy: http://127.0.0.1:7890
```

### 2. 测试控制面板
访问 http://localhost:8080，应该看到：
- ✅ 系统状态
- 🌐 域名端口映射
- 📖 快速启动按钮

### 3. 测试代理功能
点击 "Start GitHub Proxy" 按钮，或访问：
http://localhost:8080/proxy/github.com

应该会重定向到：http://localhost:8081/

### 4. 验证URL重写
在GitHub页面中，查看开发者工具的Network面板，应该看到：
- 原始链接：`https://api.github.com/xxx`
- 重写后：`http://localhost:8082/xxx`

### 5. 检查代理连接
在终端日志中应该看到：
```
🔗 Using upstream proxy: http://127.0.0.1:7890
[github.com] GET / -> https://github.com/
🔄 URL rewrite: api.github.com/user -> localhost:8082/user
```

## 🐛 故障排除

### 1. 端口被占用
```
❌ listen tcp :8081: bind: address already in use
```
**解决**: 修改 `main_simple.go` 中的端口号，或关闭占用端口的程序

### 2. 代理连接失败
```
❌ [github.com] Request failed: proxyconnect tcp: dial tcp 127.0.0.1:7890: connect: connection refused
```
**解决**: 
- 检查VPN是否启动
- 修改 `main_simple.go` 中的 `UpstreamProxy` 地址
- 或设置 `UpstreamProxy: ""` 禁用上游代理

### 3. 无法访问控制面板
**检查**: 
- 服务是否正常启动
- 端口8080是否被占用
- 防火墙设置

## 🎉 成功标志

如果一切正常，你应该能够：
1. ✅ 访问控制面板 (http://localhost:8080)
2. ✅ 启动GitHub代理并正常浏览
3. ✅ 看到URL重写日志
4. ✅ 通过你的VPN代理访问被墙网站

## 📝 下一步

开发环境测试成功后，可以：
1. 根据需要调整域名和端口配置
2. 添加更多需要代理的网站
3. 准备生产环境部署（Caddy + HTTPS）
4. 实现配置文件支持（YAML版本）

## 🔧 开发技巧

### 修改配置
编辑 `main_simple.go` 中的配置：
```go
func getDefaultConfig() *Config {
    return &Config{
        ControlPort:   8080,
        UpstreamProxy: "http://127.0.0.1:7890", // 修改这里
        DebugMode:     true,
        DomainPorts: map[string]int{
            // 添加新域名
            "new-site.com": 8095,
        },
    }
}
```

### 查看详细日志
所有请求和URL重写都会在终端显示，便于调试。

### 停止服务
按 `Ctrl+C` 优雅停止所有服务器。
